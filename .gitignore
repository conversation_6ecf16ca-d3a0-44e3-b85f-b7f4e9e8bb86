.vscode
!.vscode/settings.json
qnx_src_package.egg-info/
Pipfile.lock
tests/test-dir/
__pycache__/
*.pyc

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST


# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
xunit_results/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

