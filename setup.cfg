# See: https://godatadriven.com/blog/a-practical-guide-to-setuptools-and-pyproject-toml/
[metadata]
name = qnx_src_package
version = attr: qnx_src_package.__version__
author= BlackBerry Limited
author_email = <EMAIL>
url = https://gitlab.rim.net/qnx/infra/pypi/qnx_src_package
description = A python project to replace mainline/shiplist_utils
long_description = file: README.md
long_description_content_type = text/markdown
keywords = qnx_src_package
license = QSSL
classifiers = 
    Programming Language :: Python :: 3

[options]
package_dir=
    =src
packages = find:
zip_safe = True
include_package_data = True
install_requires =
    qnx_shiplist_py

[options.packages.find]
where=src

[options.entry_points]
console_scripts = 
    qnx_src_archive = qnx_src_package.src_archive:main
    qnx_src_package = qnx_src_package.src_package:main
    qnx_src_package_dir = qnx_src_package.src_package_dir:main

[options.extras_require]
dev = 
    bumpversion
    build
    ruff
    black
    pytest
    sphinx
    sphinx_rtd_theme
    tox
    twine
    wheel

[options.package_data]
* = README.md


