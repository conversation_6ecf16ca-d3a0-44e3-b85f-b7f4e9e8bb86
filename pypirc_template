# .pypi rc file.
# This file generally goes in $HOME/.pypirc to tell twine
# how to upload internal packages
# It is typically already setup in QNX build containers

[distutils]
index-servers = local
[local]
repository: https://pypi.bts.rim.net

# the username and password are no longer stored in this file
# instead they are passed on the twine command line using the
# variables ${PYPI_USER} and ${PYPI_PASS} 