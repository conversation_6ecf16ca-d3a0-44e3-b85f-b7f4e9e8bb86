import argparse
import logging
import os
from pathlib import Path

from qnx_shiplist_py.package import find_shiplists

from qnx_src_package.src_package import process_package
from qnx_src_package.utils import log_python_pkg_info, prepare_src_archives_dir

logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser("Process a directory of source packages")
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging (DEBUG level)")
    parser.add_argument(
        "-d", "--shiplist_dir", help="Directory containing shiplist source packages", required=True, type=Path
    )
    parser.add_argument("-c", "--code", help="Code directory", required=False)
    parser.add_argument("--sdp", help="SDP directory", required=False)
    parser.add_argument("-b", "--baseline", required=False, default=os.getenv("BASELINE_ID"))
    parser.add_argument("--skiptest", required=False, action="store_true", help="Skip testing steps")
    args = parser.parse_args()

    if not args.shiplist_dir.exists():
        parser.error(f"Shiplist directory {args.shiplist_dir} doesn't exist")
    if not args.shiplist_dir.is_dir():
        parser.error(f"Shiplist directory {args.shiplist_dir} is not a directory")
    if args.baseline is None:
        parser.error("Baseline must be specified, either using the --baseline option or BASELINE_ID env")
    args.code = Path(args.code) if args.code else None
    args.sdp = Path(args.sdp) if args.sdp else None
    return args


def process_package_dir(
    shiplist_dir,
    baseline,
    code_dir_path=None,
    sdp_dir_path=None,
    skiptest=False,
):
    logger.info(f"Processing packages in shiplist directory: {shiplist_dir}")
    qos_be = os.environ.get("QOS_BE")
    if qos_be:
        logger.debug(f"QOS_BE: {qos_be}")

    shiplists = find_shiplists(shiplist_dir, baseline, qos_be)
    logger.info(f"Found {len(shiplists)} shiplist(s) to process.")

    # Clear the source archives directory only once before processing all packages.
    prepare_src_archives_dir(clear=True)

    for shiplist in shiplists:
        # Compute the correct manifest file name.
        # Follow: https://wikis.rim.net/display/QNXInfra/Gitlab+Repo+Build+and+Shiplist+Folders#GitlabRepoBuildandShiplistFolders-SourcePackaging
        shiplist_stem = shiplist.stem  # file name without the extension
        if shiplist_stem.startswith("shiplist-"):
            new_stem = "manifest-" + shiplist_stem[len("shiplist-") :]
        else:
            new_stem = "manifest-" + shiplist_stem
        manifest_path = shiplist.parent / (new_stem + ".txt")

        # If the manifest file does not exist, skip this pair so we don't fail for other packages.
        if not manifest_path.exists():
            logger.warning(
                f"No manifest found for shiplist '{shiplist}'. Expected manifest at '{manifest_path}'. Skipping this pair."
            )
            continue

        process_package(
            manifest_path=manifest_path,
            shiplist_path=shiplist,
            code_dir_path=code_dir_path,
            sdp_dir_path=sdp_dir_path,
            baseline=baseline,
            skiptest=skiptest,
            clear_archives=False,  # Don't clear between packages else we might lose the archivess of previous packages
            # TODO: Check about possible conflicts if we get an archive with the same name
        )
    logger.info("Finished processing all packages from shiplist directory.")


def main():
    args = parse_args()
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level)
    log_python_pkg_info()
    logger.info("Starting directory source package processing")
    logger.debug(f"args: {args}")

    process_package_dir(
        shiplist_dir=args.shiplist_dir,
        baseline=args.baseline,
        code_dir_path=args.code,
        sdp_dir_path=args.sdp,
        skiptest=args.skiptest,
    )
