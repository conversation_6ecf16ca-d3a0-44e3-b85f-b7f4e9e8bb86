# -*- coding: utf-8 -*-
import os

__author__ = "BlackBerry Limited"
__email__ = "<EMAIL>"
__version__ = "0.4.1"


class CD:
    """Context manager for temporarily changing the current working
    directory similar to pushd/popd."""

    def __init__(self, new_path):
        self.new_path = new_path

    def __enter__(self):
        self.savedPath = os.getcwd()
        os.chdir(self.new_path)

    def __exit__(self, etype, value, traceback):
        os.chdir(self.savedPath)
