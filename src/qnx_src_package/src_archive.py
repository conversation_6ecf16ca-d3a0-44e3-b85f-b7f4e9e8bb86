import argparse
import glob
import logging
import os
import shutil
import subprocess
import tempfile
import zipfile
from pathlib import Path
from textwrap import dedent

from qnx_src_package import CD
from qnx_src_package.utils import get_environment_from_shell_command, get_sdp_dir, log_python_pkg_info

logger = logging.getLogger(__name__)

# Defines the metadata tags and their processing rules.
# Format: tag_name -> (processor_function, variable_name)
# - processor_function: lambda that transforms the raw value
# - variable_name: key used in the metadata dictionary
# Note: Tags must be prefixed with '#' in manifest files but not in this dict
MANIFEST_METADATA_TAGS = {
    "ARCHIVE_FOLDER_NAME": (lambda v, md: Path(v), "archive_folder_name"),
    "ARCHIVE_README": (lambda v, md: md / Path(v), "archive_readme"),
    "ADDITIONAL_ARCHIVE_CONTENT": (lambda v, md: v.split(), "additional_content_list"),
    "SKIP_COMPILE_TEST": (lambda v, md: v.lower() == "true", "skip_compile_test"),
}

IGNORE = shutil.ignore_patterns(".svn", ".git")


def parse_args():
    desc = dedent(
        """\
        Source Code Archiver
        Given a path to a source code manifest and source directory,
        create a source zipfile.

        Source manifests are text files which:
        - can define recursive content to be sourced via: "path/to/directory/*"
        - define content relative to the root of the repository
        - can have an associated README to be added to the archive
    """
    )
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument("-m", "--manifest", help="source manifest", required=True)
    parser.add_argument("-S", "--source_directory", help="source directory", required=True)

    name_help = "The name of the generated archive (default based on name of source code manifest)"
    parser.add_argument("-n", "--name", help=name_help, required=False)
    output_dir_help = "The location to generate the archive (default: CWD)"
    parser.add_argument("-o", "--output_dir", help=output_dir_help, default=os.getcwd())
    parser.add_argument("--sdp", help="SDP directory, required for archive compilation testing", required=False)
    log_choices = {"DEBUG": logging.DEBUG, "INFO": logging.INFO, "WARNING": logging.WARNING, "ERROR": logging.ERROR}
    parser.add_argument("-l", "--log_level", help="Logging level", choices=log_choices)

    args = parser.parse_args()

    source_code_path = Path(args.source_directory)
    manifest = Path(args.manifest)
    output_dir = Path(args.output_dir)
    sdp_dir_path = Path(args.sdp) if args.sdp else None

    if not source_code_path.exists():
        parser.error(f"Source code path {source_code_path} doesn't exist")
    if not source_code_path.is_dir():
        parser.error(f"Source code path {source_code_path} is not a directory")
    if not manifest.exists():
        parser.error(f"Source manifest {manifest} doesn't exist")
    if not manifest.is_file():
        parser.error(f"Source manifest path {manifest} is not a file")
    if not output_dir.exists():
        parser.error(f"Output dir {output_dir} doesn't exist")
    if not output_dir.is_dir():
        parser.error(f"Output dir {output_dir} is not a directory")
    if sdp_dir_path and not sdp_dir_path.exists():
        parser.error(f"SDP directory {sdp_dir_path} doesn't exist")
    if sdp_dir_path and not sdp_dir_path.is_dir():
        parser.error(f"SDP directory {sdp_dir_path} is not a directory")

    return args


def main():
    log_python_pkg_info()
    args = parse_args()
    source_code_path = Path(args.source_directory)
    manifest = Path(args.manifest)
    output_dir = Path(args.output_dir)

    sdp_dir_path = Path(args.sdp) if args.sdp else None
    process_manifest(
        manifest=manifest,
        source_code_path=source_code_path,
        output_dir=output_dir,
        archive_name=args.name,
        sdp_dir_path=sdp_dir_path,
    )


def directory_copy(src, dst, log_handle=None):
    if dst.is_dir():
        # Preserve cp-like behavior when copying directory into another directory
        dst = dst / src.parts[-1]
    if log_handle:
        log_handle.write(f"Copying\t{src}\nTo\t{dst}\n")
    shutil.copytree(src, dst, dirs_exist_ok=True, ignore=IGNORE)


def clean_source_directory(directory):
    """Clean build artifacts using VCS commands before copying"""
    if (directory / ".git").exists():
        logger.info(f"Cleaning Git repository: {directory}")
        try:
            with CD(directory):
                subprocess.run(["git", "clean", "-f", "-d", "-x"], check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            logger.warning(f"Error cleaning Git repository {directory}: {e}")
        return

    try:
        with CD(directory):
            subprocess.run(["svn", "info"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        logger.debug(f"Directory {directory} is not under version control")
        return

    logger.info(f"Cleaning SVN repository: {directory}")
    try:
        with CD(directory):
            result = subprocess.run(["svn", "status", "--no-ignore"], check=True, capture_output=True, text=True)

            paths_to_remove = []
            for line in result.stdout.splitlines():
                if line and line[0] in ("?", "I"):
                    # SVN status format: "?       path/to/file"
                    path = line[8:].strip() if len(line) > 8 else ""
                    if path:
                        full_path = directory / path
                        if full_path.exists():
                            paths_to_remove.append(full_path)

            for path in sorted(paths_to_remove, key=lambda p: (p.is_dir(), p), reverse=True):
                try:
                    if path.is_dir():
                        shutil.rmtree(path)
                        logger.debug(f"Removed unversioned directory: {path}")
                    else:
                        path.unlink()
                        logger.debug(f"Removed unversioned file: {path}")
                except Exception as e:
                    logger.warning(f"Failed to remove {path}: {e}")

            subprocess.run(["svn", "cleanup"], check=False, capture_output=True)

    except subprocess.CalledProcessError as e:
        logger.warning(f"Error cleaning SVN repository {directory}: {e}")


def parse_metadata(lines, manifest_dir):
    """Parse all metadata directives from manifest lines and return as a dictionary."""
    metadata = {}

    for line in lines:
        line = line.strip()
        if not line or not line.startswith("#"):
            continue

        cleaned_line = line[1:].strip()
        for tag, (processor, var_name) in MANIFEST_METADATA_TAGS.items():
            if cleaned_line.startswith(f"{tag}:"):
                value = cleaned_line.split(f"{tag}:", 1)[1].strip()

                processed_value = processor(value, manifest_dir)
                metadata[var_name] = processed_value

                if tag == "ARCHIVE_README" and not processed_value.exists():
                    raise Exception(f"ARCHIVE_README: {processed_value} not found")

                logger.info(f"{var_name.replace('_', ' ')}: {processed_value}")
                break

    return metadata


def _copy_additional_content(additional_content_list, manifest_dir, archive_folder):
    """Copy additional content to the archive folder."""
    if not additional_content_list:
        return

    for content in additional_content_list:
        if "=" in content:
            ac_source_path = manifest_dir / content.split("=", 1)[0]
            ac_dest_path = archive_folder / content.split("=", 1)[1]
        else:
            ac_source_path = manifest_dir / content
            ac_dest_path = archive_folder / content

        if not ac_source_path.exists():
            raise Exception(f"Additional content {str(ac_source_path)} not found")

        ac_dest_folder = ac_dest_path.parent
        if not ac_dest_folder.exists():
            ac_dest_folder.mkdir(parents=True, exist_ok=True)

        if ac_source_path.is_dir():
            directory_copy(ac_source_path, ac_dest_path)
        else:
            shutil.copy2(ac_source_path, ac_dest_path)


def _warn_missing_makefiles(source_code_path, archive_folder):
    """Check if important Makefiles exist in source but are missing from archive."""
    important_makefiles = ["Makefile", "qnx/build/Makefile"]
    missing_files = []
    logger.debug("Checking: Missing Makefiles")
    logger.debug(f"Source Dir: {source_code_path}")
    logger.debug(f"Archive Dir: {archive_folder}")

    for makefile in important_makefiles:
        source_file = source_code_path / makefile
        archive_file = archive_folder / makefile

        if source_file.exists() and not archive_file.exists():
            missing_files.append(makefile)

    if missing_files:
        logger.warning("Important Makefiles found in source repository but missing from manifest:")
        for missing_file in missing_files:
            logger.warning(f"  - {missing_file}")
        logger.warning("Consider adding these Makefiles to your manifest to ensure successful compilation testing")


def process_manifest(manifest, source_code_path, output_dir, archive_name=None, sdp_dir_path=None, skiptest=False):
    logger.info(f"Processing source code manifest {manifest}")
    logger.debug(f"Source code path {source_code_path}")

    if archive_name is None:
        archive = manifest.with_suffix(".zip")
        archive = Path(str(archive).replace("shiplist-", "").replace("manifest-", ""))
        archive_name = archive.name

    if (output_dir / archive_name).exists():
        logger.warning(f"Archive {archive_name} already exists in {output_dir}.")

    logger.info(f"archive name {archive_name}")
    archive_path = output_dir / archive_name
    logger.info(f"archive path {archive_path}")

    manifest_dir = manifest.parent

    lines = []
    with open(manifest) as f:
        for line in f:
            lines.append(line.strip("\n"))

    metadata = parse_metadata(lines, manifest_dir)

    archive_folder_name = metadata.get("archive_folder_name")
    archive_readme = metadata.get("archive_readme")
    additional_content_list = metadata.get("additional_content_list")
    skip_compile_test_from_manifest = metadata.get("skip_compile_test", False)

    if archive_folder_name is None:
        raise Exception("ARCHIVE_FOLDER_NAME: tag not found in source manifest")

    archive_folder = output_dir / archive_folder_name

    # Step 1: Direct copy source to archive folder
    logger.info(f"Copying entire source directory to archive folder: {archive_folder}")
    if archive_folder.exists():
        logger.info(f"Removing existing archive folder: {archive_folder}")
        shutil.rmtree(archive_folder)

    try:
        directory_copy(source_code_path, archive_folder)
        logger.info(f"Successfully copied source directory to {archive_folder}")
    except Exception as e:
        logger.error(f"Failed to copy source directory: {e}")
        raise Exception(f"Failed to copy source directory to archive folder: {e}")

    # Step 2: Clean the archive folder copy (safe since it's not the original)
    logger.info(f"Cleaning archive folder copy: {archive_folder}")
    clean_source_directory(archive_folder)

    # Step 3: Build files_to_keep set from manifest
    logger.info("Building set of files to keep based on manifest")
    files_to_keep = set()

    # Always keep the archive folder itself
    files_to_keep.add(archive_folder)

    # Process manifest entries to determine which files to keep
    with open(manifest) as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            recursive = line.endswith("/*")
            source_entry = Path(line.strip("/*")) if recursive else Path(line)
            logger.debug(f"Processing manifest entry: {source_entry}")

            source_in_archive = archive_folder / source_entry
            if not source_in_archive.exists():
                logger.warning(f"Manifest entry not found in archive copy: {source_entry}")
                continue

            if not recursive:
                if source_in_archive.is_dir():
                    # Non-recursive directory: keep only direct files
                    files_to_keep.add(source_in_archive)
                    for f in source_in_archive.iterdir():
                        if f.is_file():
                            files_to_keep.add(f)
                else:
                    # Single file
                    files_to_keep.add(source_in_archive)
            else:
                # Recursive: keep the directory and all its contents
                files_to_keep.add(source_in_archive)
                for root, dirs, files in os.walk(source_in_archive):
                    root_path = Path(root)
                    files_to_keep.add(root_path)
                    for d in dirs:
                        files_to_keep.add(root_path / d)
                    for f in files:
                        files_to_keep.add(root_path / f)

    # Also keep parent directories of files we want to keep
    files_to_keep_with_parents = set(files_to_keep)
    for file_path in files_to_keep:
        parent = file_path.parent
        while parent != archive_folder.parent and parent not in files_to_keep_with_parents:
            files_to_keep_with_parents.add(parent)
            parent = parent.parent

    # Step 4: Remove files from archive folder that are not in the "files to keep" set
    logger.info("Removing files not specified in manifest from archive folder")
    files_removed = 0
    dirs_removed = 0

    # Walk the archive folder and collect items to remove
    items_to_remove = []
    for root, dirs, files in os.walk(archive_folder, topdown=False):
        root_path = Path(root)

        # Check files first
        for f in files:
            file_path = root_path / f
            if file_path not in files_to_keep_with_parents:
                items_to_remove.append(file_path)

        # Check directories
        for d in dirs:
            dir_path = root_path / d
            if dir_path not in files_to_keep_with_parents:
                items_to_remove.append(dir_path)

    # Remove items that are not in the manifest
    for item_path in items_to_remove:
        try:
            if item_path.is_dir():
                shutil.rmtree(item_path)
                dirs_removed += 1
                logger.debug(f"Removed directory not in manifest: {item_path.relative_to(archive_folder)}")
            else:
                item_path.unlink()
                files_removed += 1
                logger.debug(f"Removed file not in manifest: {item_path.relative_to(archive_folder)}")
        except Exception as e:
            logger.warning(f"Failed to remove {item_path}: {e}")

    logger.info(f"Removed {files_removed} files and {dirs_removed} directories not specified in manifest")

    # Step 5: Add additional content (README and other supplementary files)
    logger.info("Adding additional content to archive folder")
    if archive_readme:
        try:
            shutil.copy2(archive_readme, archive_folder / "README.txt")
            logger.info(f"Added README.txt from {archive_readme}")
        except Exception as e:
            logger.error(f"Failed to copy README: {e}")
            raise Exception(f"Failed to copy README to archive: {e}")

    try:
        _copy_additional_content(additional_content_list, manifest_dir, archive_folder)
        if additional_content_list:
            logger.info(f"Added {len(additional_content_list)} additional content items")
    except Exception as e:
        logger.error(f"Failed to copy additional content: {e}")
        raise Exception(f"Failed to copy additional content to archive: {e}")

    # Warn if important Makefiles are missing from manifest
    _warn_missing_makefiles(source_code_path, archive_folder)

    # Step 6: Create archive (existing logic)
    logger.info(f"Writing zip file {archive_path}")
    try:
        with zipfile.ZipFile(archive_path, mode="w") as zip_archive:
            zip_archive.write(archive_folder, archive_folder_name)
            for root, _, files in os.walk(archive_folder):
                for file in files:
                    filename = Path(root, file)
                    archive_file_name = filename.relative_to(output_dir)
                    zip_archive.write(filename, arcname=archive_file_name)
        logger.info(f"Successfully created archive: {archive_path}")
    except Exception as e:
        logger.error(f"Failed to create zip archive: {e}")
        raise Exception(f"Failed to create zip archive: {e}")

    # Clean up archive folder after creating zip
    try:
        shutil.rmtree(archive_folder)
        logger.debug(f"Cleaned up temporary archive folder: {archive_folder}")
    except Exception as e:
        logger.warning(f"Failed to clean up archive folder {archive_folder}: {e}")

    if sdp_dir_path and not (skip_compile_test_from_manifest or skiptest):
        run_archive_compilation_test(archive_path, sdp_dir_path)
    elif not sdp_dir_path:
        logger.info("No SDP directory provided; skipping archive testing.")


def run_archive_compilation_test(archive_path, sdp_dir_path):
    logger.info(f"Testing archive {archive_path}")
    sdp_dir = get_sdp_dir(sdp_dir_path)

    if "QCONF_OVERRIDE" in os.environ:
        del os.environ["QCONF_OVERRIDE"]

    sdp_env = get_environment_from_shell_command(sdp_dir / "qnxsdp-env.sh")

    with tempfile.TemporaryDirectory() as tmpdirname:
        src_test_dir = Path(tmpdirname)
        with zipfile.ZipFile(archive_path) as zip_file:
            zip_file.extractall(src_test_dir)
        with CD(src_test_dir):
            # Expect a root directory starting with source
            source = glob.glob("source*")
            if not source:
                logger.error("No source folder found in extracted archive")
                raise Exception("No source folder found in extracted archive")
            with CD(source[0]):
                logger.info(f"Building from {os.getcwd()}")
                # Check if qnx/build/Makefile exists and prefer it
                qnx_build_makefile = Path("qnx/build/Makefile")
                if qnx_build_makefile.exists():
                    logger.info('qnx/build/Makefile found - setting make command to "make -C qnx/build install"')
                    make_cmd = "make -C qnx/build install"
                else:
                    make_cmd = "make"
                subprocess.check_call(make_cmd, shell=True, env=sdp_env)


if __name__ == "__main__":
    main()
