import argparse
import logging
import os
import sys
from pathlib import Path

import qnx_shiplist_py.package

from qnx_src_package import src_archive
from qnx_src_package.utils import get_code_dir, log_python_pkg_info, prepare_src_archives_dir

logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser("Create and test a source package")
    parser.add_argument("-m", "--manifest", help="Source manifest file", required=True)
    parser.add_argument("-s", "--shiplist", help="Shiplist XML file", required=True)
    parser.add_argument("-v", "--verbose", action="store_true", help="Enable verbose logging (DEBUG level)")
    parser.add_argument("-c", "--code", help="Code directory", required=False)
    parser.add_argument("--sdp", help="SDP directory", required=False)
    parser.add_argument("-b", "--baseline", required=False, default=os.getenv("BASELINE_ID"))
    parser.add_argument("--skiptest", required=False, action="store_true", help="Skip testing steps")

    args = parser.parse_args()
    args.manifest = Path(args.manifest)
    args.shiplist = Path(args.shiplist)

    if not args.manifest.exists():
        parser.error(f"Source manifest {args.manifest} doesn't exist")
    if not args.manifest.is_file():
        parser.error(f"Source manifest path {args.manifest} is not a file")
    if not args.shiplist.exists():
        parser.error(f"Shiplist XML {args.shiplist} doesn't exist")
    if not args.shiplist.is_file():
        parser.error(f"Shiplist XML path {args.shiplist} is not a file")

    args.code = Path(args.code) if args.code else None
    args.sdp = Path(args.sdp) if args.sdp else None

    return args


def main():
    args = parse_args()

    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level)
    log_python_pkg_info()
    logger.info("Starting source package creation")
    logger.debug(f"args: {args}")

    process_package(
        manifest_path=args.manifest,
        shiplist_path=args.shiplist,
        code_dir_path=args.code,
        sdp_dir_path=args.sdp,
        baseline=args.baseline,
        skiptest=args.skiptest,
    )
    logger.info("Finished source package creation")


def process_package(
    manifest_path,
    shiplist_path,
    code_dir_path=None,
    sdp_dir_path=None,
    baseline=None,
    skiptest=False,
    clear_archives=True,
):
    logger.info(f"Processing package with manifest {manifest_path} and shiplist {shiplist_path}")
    src_archives_dir = prepare_src_archives_dir(clear_archives)
    logger.info(f"Source archives directory: {src_archives_dir}")
    code_dir = get_code_dir(code_dir_path)
    logger.debug(
        f"Calling process_manifest with manifest {manifest_path}, code_dir {code_dir}, src_archives_dir {src_archives_dir}, sdp_dir_path {sdp_dir_path}, skiptest {skiptest}"
    )
    src_archive.process_manifest(
        manifest=manifest_path,
        source_code_path=code_dir,
        output_dir=src_archives_dir,
        sdp_dir_path=sdp_dir_path,
        skiptest=skiptest,
    )
    create_package(src_archives_dir, shiplist_path, baseline)
    logger.info("Finished processing package.")


def create_package(src_archives_dir, shiplist, baseline_arg):
    logger.info(f"Packaging {shiplist}")

    sys.argv = ["fake_program_name", "-s", str(shiplist), "-b", baseline_arg, "-S", str(src_archives_dir)]
    qnx_shiplist_py.package.package_main()
