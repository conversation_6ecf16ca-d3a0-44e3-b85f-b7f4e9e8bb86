import logging
import os
import shutil
import sys
from importlib import metadata
from pathlib import Path

logger = logging.getLogger(__name__)


def get_workspace():
    workspace = os.environ.setdefault("WORKSPACE", os.getcwd())
    if workspace == os.getcwd():
        logger.info("WORKSPACE not set. Defaulting to current directory.")
    return Path(workspace)


def get_code_dir(code_dir_arg):
    code_dir_env = os.environ.get("CODE_DIR")
    code_dir = None
    if code_dir_arg:
        if code_dir_env and code_dir_env != code_dir_arg:
            logger.warning(f"CODE_DIR from environment {code_dir_env} overridden by command-line {code_dir_arg}")
        code_dir = Path(code_dir_arg)
    elif code_dir_env:
        code_dir = Path(code_dir_env)
    else:
        workspace = get_workspace()
        code_dir = workspace / "code"
    if not code_dir.exists():
        raise FileNotFoundError(f"CODE_DIR {code_dir} not found")
    return code_dir


def get_sdp_dir(sdp_dir_arg):
    sdp_dir_env = os.environ.get("SDP_DIR")
    if sdp_dir_arg and not isinstance(sdp_dir_arg, Path):
        sdp_dir_arg = Path(sdp_dir_arg)

    sdp_dir = None
    if sdp_dir_arg:
        if sdp_dir_env and Path(sdp_dir_env) != sdp_dir_arg:
            logger.warning(f"sdp_dir from environment {sdp_dir_env} overridden by command-line {sdp_dir_arg}")
        sdp_dir = sdp_dir_arg
    elif sdp_dir_env:
        sdp_dir = Path(sdp_dir_env) if not isinstance(sdp_dir_env, Path) else sdp_dir_env
    else:
        workspace = get_workspace()
        sdp_dir = workspace / "qnx_sdp"

    if not sdp_dir.exists():
        raise FileNotFoundError(f"SDP_DIR {sdp_dir} not found")
    return sdp_dir


def prepare_src_archives_dir(clear: bool) -> Path:
    """
    Prepare and return the source archives directory.
    If 'clear' is True, delete the directory if it exists and recreate it.
    Otherwise, simply ensure the directory exists.
    """
    src_archives_dir = get_workspace() / "source_code_archives"
    if clear and src_archives_dir.exists():
        logger.debug(f"Clearing source archives directory: {src_archives_dir}")
        shutil.rmtree(src_archives_dir)
    src_archives_dir.mkdir(parents=True, exist_ok=True)
    return src_archives_dir


def get_environment_from_shell_command(script):
    """Extract the environment variables set by a script
    sourced using bash

    based on http://stackoverflow.com/questions/15276285/linux-shell-source-command-equivalent-in-python
    """
    logger.info(f"Emulating sourcing {script}")
    env = {}
    command = "bash -c 'source " + str(script) + "; env'"
    vars_before = os.environ.keys()
    logger.info("Running  {}".format(command))
    for line in os.popen(command):
        line = line.rstrip()
        (key, _, value) = line.partition("=")
        env[key] = value
    for key in set(env.keys()) - set(vars_before):
        logger.debug(f"read env var {key}={env[key]}")

    return env


def log_python_pkg_info():
    """Log package name, version and Python version."""
    try:
        version = metadata.version("qnx_src_package")
        logger.info(f"qnx_src_package version: {version}")
        logger.info(f"Python version: {sys.version.split()[0]}")
    except Exception as e:
        logger.debug(f"Unable to get package info: {e}")
