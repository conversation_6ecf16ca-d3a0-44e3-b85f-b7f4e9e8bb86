# Custom, strict linting config:
# Useful when manually checking files while refactoring

# extend base config
extend = ".ruff.toml"

# Note on maintaining/configuring this file:
# Rules get regularly added to ruff, odds are rules in this file are not comprehensive.
# It is encouraged to add rules and put them back in the template's linter.
# Some considerations before doing that:
#   Some rules do conflict/duplicate each other,
#   Avoid duplicating rules of other linting configs, the output is verbose enough
# You can always select/ignore rules specifically for your project's needs in your own project.

# Somewhat comprehensive set of useful linting rules in ruff
select = [
    "UP", # pyupgrade (upgrade to newer python version style)
    "S", # bandit (security)
    "FBT", # boolean trap
    "B", # bugbear (opiniated bug linting)
    "A", # builtins (checks for name shadowing)
    "COM", # Commas (trailing comma fixer)
    "C4", # Comprehensions
    "C90", # mccabe complexity
    # Complicated fix (but good practice)
    "DTZ", # Datetimez (cheks for unsafe datetime use)
    "EM", # errmsg (error msg formatter)
    "FA", # future-annotations
    "ISC", # implicit-str-concat
    "ICN", # import-conventions
    "G", # logging-format
    "PIE", # pie (misc. lints to simplify code)
    "T20", # print (checks for prints... okay during dev, typically bad on prod)
    "RSE", # raise
    "SLF", # self (private access)
    "SIM", # simplify
    "TCH", # type-checking (type checking block fixing)
    "ARG", # unused-arguments
    "PTH", # use-pathlib (better alternative over os)
    "PL", # Pylint (general style + errors)
    "TRY", # Tryceratops (try-except linting)
    "RUF" # Ruff rules
]

ignore = ["E501", "PTH123"]

# Allow autofix for all enabled rules (when --fix enabled)
fixable = ["E",
    "F",
    "W",
    "I",
    "D",
    "UP",
    "ANN",
    "COM",
    "C4",
    "EM",
    "FA",
    "G",
    "PIE",
    "RSE",
    "SLF",
    "SIM",
    "TCH",
    "PL",
    "RUF"
]

[flake8-type-checking]  # TCH rules
strict = true