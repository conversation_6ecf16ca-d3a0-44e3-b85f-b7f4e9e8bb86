# General linting config.
# Ruff is meant to be ran alongside a formatter, like black for example

# Selected rules to run:
# Flake8 rules: F, E, W
# Import sorting: I
# Variable names: N
# See https://docs.astral.sh/ruff/rules/ for more options.
lint.select = ["F", # Pyflakes
    "E", # Pycodestyle - errors
    "W", # Pycodestyle - warnings (includes whitespace fixers)
    "N", # pep8-naming
    "I", # isort
]
# May add "W191": Indentation contains tabs
lint.ignore = ["E501"] # Line too long

# Allow autofix for all enabled rules (when --fix enabled)
lint.fixable = ["E", "F", "W", "I"]
lint.unfixable = []

# Default config, do not fix (pre-commit subconfig overrides this)
fix = false
show-fixes = true  # If forced with --fix, show fixes

# Commonly ignored files
lint.exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]


# Black formatter recommends 88, for sight disabilities and diff tools
# 120 chosen to match legacy implementation
line-length = 120

# Ignore `E402` (import violations) in all `__init__.py` files, and in `path/to/file.py`.
[lint.per-file-ignores]
"__init__.py" = ["E402"]

[lint.mccabe]
# Default to a complexity level of 10.
max-complexity = 10
