import logging
import subprocess
import zipfile
from unittest.mock import MagicMock, patch

import pytest

from qnx_src_package.src_archive import process_manifest, run_archive_compilation_test

# =============================================================================
# TEST CONSTANTS
# =============================================================================

SIMPLE_MAKEFILE = """\
all:
\t@echo "Build successful"
"""

QNX_BUILD_MAKEFILE = """\
install:
\t@echo "QNX build install successful"
"""

SIMPLE_C_FILE = "int main() { return 0; }"

SIMPLE_MANIFEST = """\
# Test Manifest
# ARCHIVE_FOLDER_NAME:test_pkg_1
subdir/*
"""

FULL_MANIFEST = """\
# ARCHIVE_FOLDER_NAME:test_package
# ARCHIVE_README:README.txt
# ADDITIONAL_ARCHIVE_CONTENT:extra.txt
# SKIP_COMPILE_TEST:false
test/*
"""

INVALID_MANIFEST_NO_PREFIX = """\
ARCHIVE_FOLDER_NAME:test_pkg
test/*
"""

INVALID_MANIFEST_MISSING_TAG = """\
# This is a manifest without ARCHIVE_FOLDER_NAME
test/*
"""

# =============================================================================
# FIXTURES
# =============================================================================


@pytest.fixture
def dummy_archive(tmp_path):
    """Create a dummy source archive with a root Makefile."""
    archive_dir = tmp_path / "source_test"
    archive_dir.mkdir()

    (archive_dir / "Makefile").write_text(SIMPLE_MAKEFILE)
    (archive_dir / "main.c").write_text(SIMPLE_C_FILE)

    archive_path = tmp_path / "dummy_archive.zip"
    with zipfile.ZipFile(archive_path, "w") as zipf:
        for file in archive_dir.rglob("*"):
            zipf.write(file, file.relative_to(tmp_path))

    return archive_path


@pytest.fixture
def qnx_archive(tmp_path):
    """Create an archive with qnx/build/Makefile."""
    archive_dir = tmp_path / "source_qnx"
    qnx_build_dir = archive_dir / "qnx" / "build"
    qnx_build_dir.mkdir(parents=True)

    (archive_dir / "Makefile").write_text(SIMPLE_MAKEFILE)
    (qnx_build_dir / "Makefile").write_text(QNX_BUILD_MAKEFILE)
    (archive_dir / "main.c").write_text(SIMPLE_C_FILE)

    archive_path = tmp_path / "qnx_archive.zip"
    with zipfile.ZipFile(archive_path, "w") as zipf:
        for file in archive_dir.rglob("*"):
            zipf.write(file, file.relative_to(tmp_path))

    return archive_path


@pytest.fixture
def mock_sdp_dir(tmp_path):
    """Create a mock SDP directory with qnxsdp-env.sh script."""
    sdp_dir = tmp_path / "qnx_sdp"
    sdp_dir.mkdir()

    script = sdp_dir / "qnxsdp-env.sh"
    script.write_text(
        """\
#!/bin/bash
export QNX_HOST=/mock/qnx_host
export QNX_TARGET=/mock/qnx_target
"""
    )
    script.chmod(0o755)

    return sdp_dir


# =============================================================================
# TESTS
# =============================================================================


def test_run_archive_compilation_test_success_root_makefile(dummy_archive, mock_sdp_dir):
    """Test successful compilation with root Makefile."""
    mock_check_call = MagicMock()

    with patch("subprocess.check_call", mock_check_call):
        run_archive_compilation_test(dummy_archive, mock_sdp_dir)

        mock_check_call.assert_called_once()
        # Should use regular make command
        args = mock_check_call.call_args[0][0]
        assert args == "make"

        env = mock_check_call.call_args[1]["env"]
        assert env["QNX_HOST"] == "/mock/qnx_host"
        assert env["QNX_TARGET"] == "/mock/qnx_target"


def test_run_archive_compilation_test_success_qnx_build_makefile(qnx_archive, mock_sdp_dir):
    """Test successful compilation with qnx/build/Makefile."""
    mock_check_call = MagicMock()

    with patch("subprocess.check_call", mock_check_call):
        run_archive_compilation_test(qnx_archive, mock_sdp_dir)

        mock_check_call.assert_called_once()
        # Should use QNX build command
        args = mock_check_call.call_args[0][0]
        assert args == "make -C qnx/build install"

        env = mock_check_call.call_args[1]["env"]
        assert env["QNX_HOST"] == "/mock/qnx_host"
        assert env["QNX_TARGET"] == "/mock/qnx_target"


def test_run_archive_compilation_test_missing_source_dir(tmp_path, mock_sdp_dir, caplog):
    """Test error when archive has no source directory."""
    caplog.set_level(logging.ERROR)

    bad_archive = tmp_path / "bad.zip"
    with zipfile.ZipFile(bad_archive, "w") as zipf:
        zipf.writestr("other/readme.txt", "Not a source dir")

    with pytest.raises(Exception, match="No source folder found"):
        run_archive_compilation_test(bad_archive, mock_sdp_dir)


def test_run_archive_compilation_test_make_failure(dummy_archive, mock_sdp_dir):
    """Test error handling when make fails."""
    mock_check_call = MagicMock(side_effect=subprocess.CalledProcessError(1, "make"))

    with patch("subprocess.check_call", mock_check_call):
        with pytest.raises(subprocess.CalledProcessError):
            run_archive_compilation_test(dummy_archive, mock_sdp_dir)


@pytest.mark.parametrize("vcs_type", [("git", ".git"), ("svn", ".svn")])
def test_process_manifest_vcs_clean_removes_artifacts(tmp_path, vcs_type):
    """Test VCS clean removes build artifacts."""
    vcs_name, vcs_dir_name = vcs_type

    source_dir = tmp_path / "source_root"
    sub_dir = source_dir / "subdir"
    sub_dir.mkdir(parents=True)
    (sub_dir / "main.c").write_text(SIMPLE_C_FILE)
    (sub_dir / "main.o").write_text("binary object data")

    (source_dir / vcs_dir_name).mkdir()

    manifest_path = tmp_path / "manifest.txt"
    manifest_path.write_text(SIMPLE_MANIFEST)

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    def mock_run_side_effect(args, **kwargs):
        if vcs_name == "svn":
            if args[1] == "info":
                return subprocess.CompletedProcess(args, 0, stdout=b"", stderr=b"")
            elif args[1] == "status":
                if (sub_dir / "main.o").exists():
                    return subprocess.CompletedProcess(args, 0, stdout="?       subdir/main.o\n", stderr=b"")
                return subprocess.CompletedProcess(args, 0, stdout="", stderr=b"")
        elif vcs_name == "git" and args[1] == "clean":
            if (sub_dir / "main.o").exists():
                (sub_dir / "main.o").unlink()
        return subprocess.CompletedProcess(args, 0, stdout=b"", stderr=b"")

    with patch("subprocess.run", MagicMock(side_effect=mock_run_side_effect)):
        process_manifest(
            manifest=manifest_path,
            source_code_path=source_dir,
            output_dir=output_dir,
            archive_name=f"{vcs_name}_archive.zip",
            skiptest=True,
        )

    with zipfile.ZipFile(output_dir / f"{vcs_name}_archive.zip", "r") as zipf:
        files = zipf.namelist()
        assert any("main.c" in f for f in files)
        assert not any(".o" in f for f in files)


def test_process_manifest_with_all_directives(tmp_path):
    """Test manifest processing with all metadata directives."""
    source_dir = tmp_path / "source_root"
    test_dir = source_dir / "test"
    test_dir.mkdir(parents=True)
    (test_dir / "main.c").write_text(SIMPLE_C_FILE)
    (test_dir / "utils.c").write_text("void utils() {}")

    (tmp_path / "README.txt").write_text("Test README")
    (tmp_path / "extra.txt").write_text("Extra content")

    manifest_path = tmp_path / "manifest.txt"
    manifest_path.write_text(FULL_MANIFEST)

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    process_manifest(
        manifest=manifest_path,
        source_code_path=source_dir,
        output_dir=output_dir,
        archive_name="test.zip",
        skiptest=True,
    )

    with zipfile.ZipFile(output_dir / "test.zip", "r") as zipf:
        files = zipf.namelist()
        assert "test_package/test/main.c" in files
        assert "test_package/test/utils.c" in files
        assert "test_package/README.txt" in files
        assert "test_package/extra.txt" in files


@pytest.mark.parametrize(
    "manifest_content,error_match",
    [
        (INVALID_MANIFEST_NO_PREFIX, "ARCHIVE_FOLDER_NAME: tag not found"),
        (INVALID_MANIFEST_MISSING_TAG, "ARCHIVE_FOLDER_NAME: tag not found"),
    ],
)
def test_process_manifest_invalid_raises_error(tmp_path, manifest_content, error_match):
    """Test invalid manifests raise appropriate errors."""
    source_dir = tmp_path / "source"
    source_dir.mkdir()
    (source_dir / "test.c").write_text(SIMPLE_C_FILE)

    manifest_path = tmp_path / "manifest.txt"
    manifest_path.write_text(manifest_content)

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    with pytest.raises(Exception, match=error_match):
        process_manifest(
            manifest=manifest_path,
            source_code_path=source_dir,
            output_dir=output_dir,
            skiptest=True,
        )
