# Test using scm clients installed on the system
import subprocess
import zipfile

import pytest

from qnx_src_package.src_archive import process_manifest

# =============================================================================
# TEST CONSTANTS
# =============================================================================
#
# code/                                          (Git/.svn repository root)
# ├── Makefile                                   (ROOT_MAKEFILE)
# ├── hardware/
# │   ├── Makefile                              (HARDWARE_MAKEFILE)
# │   └── boards/
# │       ├── test-board/
# │       │   ├── board.c                       (BOARD_C_FILE)
# │       │   └── board.o                       (build artifact - should be cleaned)
# │       └── test.log                          (build log - should be cleaned)
# ├── qnx/
# │   ├── build/
# │   │   └── Makefile                           (If this exists, then this should be preferred over the one in the root directory)
# │   │   └── qnxbuild.yml                       (Defines the QNX build overrides, used by qnxbuild :irrelevant for qnx_src_package) https://wikis.rim.net/display/QNXInfra/qnxbuild#qnxbuild-Configuration
# │   └── shiplist/
# │       └── source_packages/
# │           └── manifest-code.txt             (MANIFEST_CONTENT)
# │           └── manifest-code.xml             (Defines the source package, used by qnx_shiplist_py :irrelevant for qnx_src_package)
# └── .git/ or .svn/                           (VCS metadata)

# File Contents
ROOT_MAKEFILE = """\
# Root Makefile
CC=gcc
CFLAGS=-Wall

all: hardware

hardware:
\t$(MAKE) -C hardware

clean:
\t$(MAKE) -C hardware clean
"""

HARDWARE_MAKEFILE = """\
# Hardware Makefile
CC=gcc
CFLAGS=-Wall

all: boards

boards:
\t$(MAKE) -C boards

clean:
\t$(MAKE) -C boards clean
"""

BOARD_C_FILE = """\
#include <stdio.h>
int board_init() {
    printf("Board initialized\\n");
    return 0;
}
int main() {
    return board_init();
}
"""

MANIFEST_CONTENT = """\
# QNX Source Package Manifest
# ARCHIVE_FOLDER_NAME:qnx_hardware_package
Makefile
hardware/Makefile
hardware/boards/*
"""


def is_command_available(command):
    """Check if a command is available in the system PATH."""
    try:
        subprocess.run([command, "--version"], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False


# Command availability checks
git_available = is_command_available("git")
svn_available = is_command_available("svn")
make_available = is_command_available("make")

# =============================================================================
# FIXTURES
# =============================================================================


@pytest.fixture
def code_repo(request, tmp_path):
    """Create a comprehensive code repository with VCS (git or svn) and build artifacts."""
    vcs_type = request.param

    if vcs_type == "git" and not git_available:
        pytest.skip("Git not available")
    if vcs_type == "svn" and not svn_available:
        pytest.skip("SVN not available")

    # Create the complete directory structure
    code_dir = tmp_path / "code"
    hardware_dir = code_dir / "hardware"
    boards_dir = hardware_dir / "boards"
    board_dir = boards_dir / "test-board"
    manifest_dir = code_dir / "qnx" / "shiplist" / "source_packages"

    # Create all directories
    board_dir.mkdir(parents=True)
    manifest_dir.mkdir(parents=True)

    # Create all source files
    (code_dir / "Makefile").write_text(ROOT_MAKEFILE)
    (hardware_dir / "Makefile").write_text(HARDWARE_MAKEFILE)
    (board_dir / "board.c").write_text(BOARD_C_FILE)

    # Create manifest file
    (manifest_dir / "manifest-code.txt").write_text(MANIFEST_CONTENT)

    if vcs_type == "git":
        # Initialize git repo
        env = {
            "GIT_AUTHOR_NAME": "Test User",
            "GIT_AUTHOR_EMAIL": "<EMAIL>",
            "GIT_COMMITTER_NAME": "Test User",
            "GIT_COMMITTER_EMAIL": "<EMAIL>",
        }
        subprocess.run(["git", "init"], cwd=code_dir, check=True, capture_output=True, env=env)
        subprocess.run(["git", "add", "."], cwd=code_dir, check=True, capture_output=True, env=env)
        subprocess.run(
            ["git", "commit", "-m", "Initial commit"], cwd=code_dir, check=True, capture_output=True, env=env
        )

        return code_dir

    else:  # svn
        repo_path = tmp_path / "svn_repo"  # SVN repository (server-side storage)
        work_path = tmp_path / "svn_work"  # SVN working copy (client checkout with .svn dirs)

        # Create repository
        subprocess.run(["svnadmin", "create", str(repo_path)], check=True, capture_output=True)

        # Import the entire code structure
        repo_url = f"file://{repo_path.absolute()}"
        subprocess.run(
            ["svn", "import", str(code_dir), repo_url, "-m", "Initial import"], check=True, capture_output=True
        )

        # Remove the original and checkout working copy
        import shutil

        shutil.rmtree(code_dir)

        subprocess.run(["svn", "checkout", repo_url, str(work_path)], check=True, capture_output=True)

        return work_path


# =============================================================================
# TESTS
# =============================================================================


@pytest.mark.skipif(not make_available, reason="make not available")
@pytest.mark.parametrize("code_repo", ["git", "svn"], indirect=True)
def test_vcs_clean_removes_build_artifacts(code_repo, tmp_path):
    """Test that VCS clean operations remove build artifacts before archiving."""

    # Create build artifacts manually (simulating make process)
    board_dir = code_repo / "hardware" / "boards" / "test-board"
    boards_dir = code_repo / "hardware" / "boards"

    (board_dir / "board.o").write_bytes(b"fake object file")
    (board_dir / "board").write_bytes(b"fake executable")  # compiled binary
    (boards_dir / "test.log").write_text("build log output")

    # Verify artifacts exist before cleaning
    assert (board_dir / "board.o").exists(), "Build artifact should exist"
    assert (boards_dir / "test.log").exists(), "Log file should exist"

    manifest_path = code_repo / "qnx" / "shiplist" / "source_packages" / "manifest-code.txt"
    assert manifest_path.exists(), "Manifest should exist"

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    # Process manifest (should clean VCS artifacts)
    process_manifest(
        manifest=manifest_path,
        source_code_path=code_repo,
        output_dir=output_dir,
        archive_name="qnx_package.zip",
        skiptest=True,
    )

    # Verify archive contents match expected structure (without build artifacts)
    archive_path = output_dir / "qnx_package.zip"
    assert archive_path.exists()

    with zipfile.ZipFile(archive_path, "r") as zipf:
        archived_files = zipf.namelist()

        print("Archived files:", sorted(archived_files))

        # Source files should be included
        assert any("qnx_hardware_package/Makefile" in path for path in archived_files)
        assert any("qnx_hardware_package/hardware/Makefile" in path for path in archived_files)
        assert any("qnx_hardware_package/hardware/boards/test-board/board.c" in path for path in archived_files)

        # Build artifacts should NOT be included
        assert not any(".o" in path for path in archived_files), "Object files should be cleaned"
        assert not any("test.log" in path for path in archived_files), "Log files should be cleaned"
        assert not any(path.endswith("/board") for path in archived_files), "Compiled binaries should be cleaned"

        # Manifest itself should NOT be in the archive
        assert not any("manifest-code.txt" in path for path in archived_files), "Manifest should not be archived"


@pytest.mark.skipif(not svn_available, reason="SVN not available")
def test_svn_handles_ignored_and_unversioned_files(tmp_path):
    """Test that SVN properly handles both ignored (I) and unversioned (?) files."""

    code_dir = tmp_path / "code"
    hardware_dir = code_dir / "hardware"
    boards_dir = hardware_dir / "boards"
    board_dir = boards_dir / "test-board"
    manifest_dir = code_dir / "qnx" / "shiplist" / "source_packages"

    board_dir.mkdir(parents=True)
    manifest_dir.mkdir(parents=True)

    (code_dir / "Makefile").write_text(ROOT_MAKEFILE)
    (hardware_dir / "Makefile").write_text(HARDWARE_MAKEFILE)
    (board_dir / "board.c").write_text(BOARD_C_FILE)
    (manifest_dir / "manifest-code.txt").write_text(MANIFEST_CONTENT)

    repo_path = tmp_path / "svn_repo"  # SVN repository (server-side storage)
    work_path = tmp_path / "svn_work"  # SVN working copy (client checkout with .svn dirs)

    subprocess.run(["svnadmin", "create", str(repo_path)], check=True, capture_output=True)

    repo_url = f"file://{repo_path.absolute()}"
    subprocess.run(["svn", "import", str(code_dir), repo_url, "-m", "Initial import"], check=True, capture_output=True)

    import shutil

    shutil.rmtree(code_dir)

    subprocess.run(["svn", "checkout", repo_url, str(work_path)], check=True, capture_output=True)

    # Set up SVN ignore patterns for build artifacts
    subprocess.run(
        ["svn", "propset", "svn:ignore", "*.o\n*.log\ntemp*", str(work_path)], check=True, capture_output=True
    )
    subprocess.run(["svn", "commit", "-m", "Add ignore patterns"], cwd=work_path, check=True, capture_output=True)

    work_board_dir = work_path / "hardware" / "boards" / "test-board"
    work_boards_dir = work_path / "hardware" / "boards"

    # Files that will be ignored (I) - match ignore patterns
    (work_board_dir / "board.o").write_bytes(b"object file")  # Matches *.o
    (work_boards_dir / "build.log").write_text("build log")  # Matches *.log
    (work_path / "temp_cache.txt").write_text("temp file")  # Matches temp*

    # Files that will be unversioned (?) - don't match ignore patterns
    (work_path / "unversioned.txt").write_text("not ignored")  # Will be unversioned (?)
    (work_path / "random").mkdir()  # Unversioned directory
    (work_path / "random" / "file.txt").write_text("random")  # File in unversioned dir

    # Use the existing manifest from the comprehensive structure
    manifest_path = work_path / "qnx" / "shiplist" / "source_packages" / "manifest-code.txt"
    assert manifest_path.exists(), "Manifest should exist in the structure"

    output_dir = tmp_path / "output"
    output_dir.mkdir()

    # Process manifest (should clean both ignored and unversioned files)
    process_manifest(
        manifest=manifest_path,
        source_code_path=work_path,
        output_dir=output_dir,
        archive_name="svn_package.zip",
        skiptest=True,
    )

    archive_path = output_dir / "svn_package.zip"
    assert archive_path.exists()

    with zipfile.ZipFile(archive_path, "r") as zipf:
        archived_files = zipf.namelist()

        print("SVN archived files:", sorted(archived_files))

        # Versioned source files should be included (from manifest entries)
        assert any("qnx_hardware_package/Makefile" in path for path in archived_files)
        assert any("qnx_hardware_package/hardware/Makefile" in path for path in archived_files)
        assert any("qnx_hardware_package/hardware/boards/test-board/board.c" in path for path in archived_files)

        # Ignored files should NOT be included (I status)
        assert not any("board.o" in path for path in archived_files), "Ignored .o files should not be archived"
        assert not any("build.log" in path for path in archived_files), "Ignored .log files should not be archived"
        assert not any(
            "temp_cache.txt" in path for path in archived_files
        ), "Ignored temp* files should not be archived"

        # Unversioned files should NOT be included (? status)
        assert not any("unversioned.txt" in path for path in archived_files), "Unversioned files should not be archived"
        assert not any("random" in path for path in archived_files), "Unversioned directories should not be archived"

        # Manifest itself should NOT be in the archive (not in manifest entries)
        assert not any("manifest-code.txt" in path for path in archived_files), "Manifest should not be archived"
