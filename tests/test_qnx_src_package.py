#!/usr/bin/env python
# -*- coding: utf-8 -*-
import zipfile

from qnx_src_package import src_archive

"""
test_qnx_src_package
----------------------------------

Tests for `qnx_src_package` module.
"""


def test_process_manifest_creates_valid_archive(tmp_path):
    """Test processing manifest and verifying archive content."""
    root = tmp_path / "test1_root"
    root.mkdir()

    code_dir = root / "code"
    code_dir.mkdir()

    manifest_dir = root / "manifests"
    manifest_dir.mkdir()

    output_dir = root / "output_dir"
    output_dir.mkdir()

    readme_dir = manifest_dir / "readme"
    readme_dir.mkdir()
    readme_file = readme_dir / "Readme_file.txt"
    readme_content = "README file"
    readme_file.write_text(readme_content)

    for sub_dir in ["sub_dir1", "sub_dir2", "sub_dir3"]:
        (code_dir / sub_dir).mkdir()

    for fname in ["f1a", "f1b", "f1c"]:
        (code_dir / "sub_dir1" / fname).touch()

    for fname in ["f2a", "f2b", "f2c"]:
        (code_dir / "sub_dir2" / fname).touch()

    for fname in ["f3a", "f3b", "f3c"]:
        (code_dir / "sub_dir3" / fname).touch()

    (manifest_dir / "ac1").touch()

    manifest_content = """\
# Source package test_example_1
# = = = = = = = = = = =
# ARCHIVE_FOLDER_NAME:test_example_1_folder
# ARCHIVE_README:readme/Readme_file.txt
# ADDITIONAL_ARCHIVE_CONTENT:ac1=ac1
# = = = = = = = = = = =
sub_dir1/*
sub_dir2/f2a
sub_dir2/f2b
sub_dir3/f3a
"""
    source_manifest = manifest_dir / "test_example_1_manifest.txt"
    source_manifest.write_text(manifest_content)

    # Create a dummy SDP directory to satisfy the new parameter requirement.
    # The contents of this directory won't be used since we're skipping the compile test.
    dummy_sdp = tmp_path / "qnxsdp"
    dummy_sdp.mkdir()

    # Pass dummy_sdp as the sdp_dir_path, and set skiptest=True to avoid testing the archive.
    src_archive.process_manifest(source_manifest, code_dir, output_dir, sdp_dir_path=dummy_sdp, skiptest=True)

    archive_folder = output_dir / "test_example_1_folder"
    assert archive_folder.is_dir()

    archive_readme = archive_folder / "README.txt"
    assert archive_readme.is_file()
    assert archive_readme.read_text().strip() == readme_content

    archive_additional = archive_folder / "ac1"
    assert archive_additional.is_file()

    archived_sub_dir1 = archive_folder / "sub_dir1"
    for fname in ["f1a", "f1b", "f1c"]:
        assert (archived_sub_dir1 / fname).is_file()

    archived_sub_dir2 = archive_folder / "sub_dir2"
    for fname in ["f2a", "f2b"]:
        assert (archived_sub_dir2 / fname).is_file()
    assert not (archived_sub_dir2 / "f2c").exists()

    archived_sub_dir3 = archive_folder / "sub_dir3"
    assert (archived_sub_dir3 / "f3a").is_file()
    for fname in ["f3b", "f3c"]:
        assert not (archived_sub_dir3 / fname).exists()

    zip_file = output_dir / "test_example_1_manifest.zip"
    assert zip_file.is_file()

    with zipfile.ZipFile(zip_file, "r") as zf:
        zip_contents = zf.namelist()
        expected_entries = [
            "test_example_1_folder/README.txt",
            "test_example_1_folder/ac1",
            "test_example_1_folder/sub_dir1/f1a",
            "test_example_1_folder/sub_dir1/f1b",
            "test_example_1_folder/sub_dir1/f1c",
            "test_example_1_folder/sub_dir2/f2a",
            "test_example_1_folder/sub_dir2/f2b",
            "test_example_1_folder/sub_dir3/f3a",
        ]
        for entry in expected_entries:
            assert entry in zip_contents


def test_process_manifest_additional_content_nested(tmp_path):
    """Test that nested ADDITIONAL_ARCHIVE_CONTENT creates parent dirs and copies file correctly."""
    # Setup test directories
    root = tmp_path / "nested_test_root"
    root.mkdir()
    code_dir = root / "code"
    code_dir.mkdir()
    manifest_dir = root / "manifests"
    manifest_dir.mkdir()
    output_dir = root / "output_dir"
    output_dir.mkdir()
    # Create readme file
    readme_dir = manifest_dir / "readme"
    readme_dir.mkdir()
    readme_file = readme_dir / "Readme_file.txt"
    readme_content = "README content"
    readme_file.write_text(readme_content)
    # Create nested source file for additional content
    nested_src_dir = manifest_dir / "nested1" / "nested2"
    nested_src_dir.mkdir(parents=True)
    nested_file = nested_src_dir / "file.txt"
    nested_file_content = "nested content"
    nested_file.write_text(nested_file_content)
    # Write manifest with nested additional content
    manifest_lines = [
        "# Test nested ADDITIONAL_ARCHIVE_CONTENT",
        "# ARCHIVE_FOLDER_NAME:nested_test_folder",
        "# ARCHIVE_README:readme/Readme_file.txt",
        "# ADDITIONAL_ARCHIVE_CONTENT:nested1/nested2/file.txt=nested1/nested2/file.txt",
    ]
    source_manifest = manifest_dir / "nested_manifest.txt"
    source_manifest.write_text("\n".join(manifest_lines))
    # Create dummy SDP to bypass compile test
    dummy_sdp = tmp_path / "qnxsdp"
    dummy_sdp.mkdir()
    # Run process_manifest
    src_archive.process_manifest(source_manifest, code_dir, output_dir, sdp_dir_path=dummy_sdp, skiptest=True)
    # Verify nested additional content file exists in output archive folder
    dest_file = output_dir / "nested_test_folder" / "nested1" / "nested2" / "file.txt"
    assert dest_file.is_file()
    assert dest_file.read_text() == nested_file_content
