import logging
import os
import tempfile
from pathlib import Path
from unittest import mock

import pytest

from qnx_src_package.src_package_dir import parse_args, process_package_dir

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG)


@pytest.fixture(autouse=True)
def mock_settings_env_vars(tmp_path):
    """Environment variables to mock to keep tests consistent"""
    stage = tmp_path / "stage"
    workspace = tmp_path / "workspace"
    stage.mkdir()
    workspace.mkdir()
    mock_vars = {
        "STAGE_DIR": str(stage),
        "WORKSPACE": str(workspace),
        "USER_TIMESTAMP": "Feb 21, 2025",
        "TIMESTAMP": "202502211551",
    }
    with mock.patch.dict(os.environ, mock_vars):
        yield


def create_source_package_structure(tmp_path):
    """Create a source package directory structure following QNX conventions.

    Structure:
    qnx/shiplist/source_packages/
    ├── shiplist-pkg1.xml
    ├── manifest-pkg1.txt
    ├── common/
    │   ├── shiplist-common1.xml
    │   └── manifest-common1.txt
    ├── sdp_mainline/
    │   ├── shiplist-mainline1.xml
    │   └── manifest-mainline1.txt
    └── qnx800/
        ├── shiplist-800pkg1.xml
        └── manifest-800pkg1.txt
    """
    source_packages_dir = tmp_path / "qnx" / "shiplist" / "source_packages"
    common_dir = source_packages_dir / "common"
    mainline_dir = source_packages_dir / "sdp_mainline"
    qnx800_dir = source_packages_dir / "qnx800"

    # Create directories
    for d in [source_packages_dir, common_dir, mainline_dir, qnx800_dir]:
        d.mkdir(parents=True, exist_ok=True)

    # Create shiplist and manifest pairs
    packages = {source_packages_dir: "pkg1", common_dir: "common1", mainline_dir: "mainline1", qnx800_dir: "800pkg1"}

    for dir_path, pkg_name in packages.items():
        shiplist = dir_path / f"shiplist-{pkg_name}.xml"
        manifest = dir_path / f"manifest-{pkg_name}.txt"
        shiplist.touch()
        manifest.touch()

    return source_packages_dir


@mock.patch("qnx_src_package.src_package_dir.process_package")
@mock.patch("qnx_src_package.src_package_dir.prepare_src_archives_dir")
def test_process_package_dir_mainline(mock_prepare_archives, mock_process_package, tmp_path):
    """Test processing source packages for sdp_mainline baseline"""
    source_packages_dir = create_source_package_structure(tmp_path)

    process_package_dir(source_packages_dir, baseline="sdp_mainline")

    # Verify prepare_src_archives_dir was called once with clear=True
    mock_prepare_archives.assert_called_once_with(clear=True)

    # Verify process_package was called for root and mainline packages
    expected_calls = [
        mock.call(
            manifest_path=source_packages_dir / "manifest-pkg1.txt",
            shiplist_path=source_packages_dir / "shiplist-pkg1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="sdp_mainline",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "common" / "manifest-common1.txt",
            shiplist_path=source_packages_dir / "common" / "shiplist-common1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="sdp_mainline",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "sdp_mainline" / "manifest-mainline1.txt",
            shiplist_path=source_packages_dir / "sdp_mainline" / "shiplist-mainline1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="sdp_mainline",
            skiptest=False,
            clear_archives=False,
        ),
    ]
    assert mock_process_package.call_count == len(expected_calls)
    mock_process_package.assert_has_calls(expected_calls, any_order=True)


@mock.patch("qnx_src_package.src_package_dir.process_package")
@mock.patch("qnx_src_package.src_package_dir.prepare_src_archives_dir")
def test_process_package_dir_qnx800(mock_prepare_archives, mock_process_package, tmp_path):
    """Test processing source packages for qnx800 baseline"""
    source_packages_dir = create_source_package_structure(tmp_path)

    process_package_dir(source_packages_dir, baseline="qnx800")

    # Verify prepare_src_archives_dir was called once with clear=True
    mock_prepare_archives.assert_called_once_with(clear=True)

    # Verify process_package was called for root and qnx800 packages
    expected_calls = [
        mock.call(
            manifest_path=source_packages_dir / "manifest-pkg1.txt",
            shiplist_path=source_packages_dir / "shiplist-pkg1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "common" / "manifest-common1.txt",
            shiplist_path=source_packages_dir / "common" / "shiplist-common1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "qnx800" / "manifest-800pkg1.txt",
            shiplist_path=source_packages_dir / "qnx800" / "shiplist-800pkg1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
    ]
    assert mock_process_package.call_count == len(expected_calls)
    mock_process_package.assert_has_calls(expected_calls, any_order=True)


@mock.patch("qnx_src_package.src_package_dir.process_package")
@mock.patch("qnx_src_package.src_package_dir.prepare_src_archives_dir")
def test_process_package_dir_with_missing_manifest(mock_prepare_archives, mock_process_package, tmp_path, caplog):
    """Test that packages with missing manifest files are skipped"""
    caplog.set_level(logging.WARNING)
    source_packages_dir = create_source_package_structure(tmp_path)

    # Remove one manifest file
    manifest_path = source_packages_dir / "manifest-pkg1.txt"
    manifest_path.unlink()

    process_package_dir(source_packages_dir, baseline="sdp_mainline")

    # Verify prepare_src_archives_dir was called once with clear=True
    mock_prepare_archives.assert_called_once_with(clear=True)

    # Verify warning was logged about missing manifest
    expected_warning = f"No manifest found for shiplist '{source_packages_dir / 'shiplist-pkg1.xml'}'. Expected manifest at '{manifest_path}'. Skipping this pair."
    assert expected_warning in caplog.text

    # Verify process_package was called only for packages with valid manifest files
    expected_calls = [
        mock.call(
            manifest_path=source_packages_dir / "common" / "manifest-common1.txt",
            shiplist_path=source_packages_dir / "common" / "shiplist-common1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="sdp_mainline",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "sdp_mainline" / "manifest-mainline1.txt",
            shiplist_path=source_packages_dir / "sdp_mainline" / "shiplist-mainline1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="sdp_mainline",
            skiptest=False,
            clear_archives=False,
        ),
    ]
    assert mock_process_package.call_count == len(expected_calls)
    mock_process_package.assert_has_calls(expected_calls, any_order=True)


@mock.patch("qnx_src_package.src_package_dir.process_package")
@mock.patch("qnx_src_package.src_package_dir.prepare_src_archives_dir")
def test_process_package_dir_with_qos_be(mock_prepare_archives, mock_process_package, tmp_path):
    """Test processing source packages with QOS_BE environment variable set"""
    source_packages_dir = create_source_package_structure(tmp_path)

    # Create QOS directory structure
    qos_dir = source_packages_dir / "qnx800" / "qos226"
    qos_dir.mkdir(parents=True)
    shiplist = qos_dir / "shiplist-qos226pkg.xml"
    manifest = qos_dir / "manifest-qos226pkg.txt"
    shiplist.touch()
    manifest.touch()

    with mock.patch.dict(os.environ, {"QOS_BE": "qos226"}):
        process_package_dir(source_packages_dir, baseline="qnx800")

    # Verify prepare_src_archives_dir was called once with clear=True
    mock_prepare_archives.assert_called_once_with(clear=True)

    # Verify process_package was called for root, qnx800, and qos226 packages
    expected_calls = [
        mock.call(
            manifest_path=source_packages_dir / "manifest-pkg1.txt",
            shiplist_path=source_packages_dir / "shiplist-pkg1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "common" / "manifest-common1.txt",
            shiplist_path=source_packages_dir / "common" / "shiplist-common1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "qnx800" / "manifest-800pkg1.txt",
            shiplist_path=source_packages_dir / "qnx800" / "shiplist-800pkg1.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
        mock.call(
            manifest_path=source_packages_dir / "qnx800" / "qos226" / "manifest-qos226pkg.txt",
            shiplist_path=source_packages_dir / "qnx800" / "qos226" / "shiplist-qos226pkg.xml",
            code_dir_path=None,
            sdp_dir_path=None,
            baseline="qnx800",
            skiptest=False,
            clear_archives=False,
        ),
    ]
    assert mock_process_package.call_count == len(expected_calls)
    mock_process_package.assert_has_calls(expected_calls, any_order=True)


@mock.patch("qnx_src_package.src_package_dir.find_shiplists")
def test_process_package_dir_empty_directory(mock_find_shiplists, caplog):
    """Test empty shiplist directory handling"""
    caplog.set_level(logging.INFO)
    with tempfile.TemporaryDirectory() as empty_dir:
        mock_find_shiplists.return_value = []
        process_package_dir(Path(empty_dir), baseline="qnx800")

        assert "Found 0 shiplist(s)" in caplog.text
        assert "Finished processing all packages" in caplog.text


@mock.patch("qnx_src_package.src_package_dir.prepare_src_archives_dir")
@mock.patch("qnx_src_package.src_package_dir.process_package")
@mock.patch("qnx_src_package.src_package_dir.find_shiplists")
def test_process_package_dir_baseline_validation(
    mock_find_shiplists, mock_process_package, mock_prepare_archives, tmp_path
):
    """Test that baseline validation works correctly"""
    source_packages_dir = tmp_path / "source_packages"
    source_packages_dir.mkdir()

    # Test with no baseline provided (should use BASELINE_ID from environment)
    mock_find_shiplists.return_value = []
    mock_prepare_archives.reset_mock()
    mock_process_package.reset_mock()

    # Test with explicitly provided baseline (should ignore environment variable)
    with mock.patch.dict(os.environ, {"BASELINE_ID": "qnx700"}):
        process_package_dir(source_packages_dir, baseline="qnx800")
        mock_find_shiplists.assert_called_once_with(source_packages_dir, "qnx800", None)


def test_parse_args_missing_baseline(tmp_path, capsys):
    """Test that parse_args raises an error when baseline is not provided via argument or environment."""
    test_dir = tmp_path / "test_dir"
    test_dir.mkdir()

    # Test with neither --baseline argument nor BASELINE_ID environment variable
    with mock.patch("sys.argv", ["script_name", "-d", str(test_dir)]):
        with mock.patch.dict(os.environ, {}, clear=True):  # Clear environment variables
            with pytest.raises(SystemExit):
                parse_args()

            # Check the error message
            captured = capsys.readouterr()
            assert "Baseline must be specified, either using the --baseline option or BASELINE_ID env" in captured.err
