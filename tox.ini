[tox]
envlist = py{3}

[testenv]
setenv =
    PIP_EXTRA_INDEX_URL=https://pypi.bts.rim.net/simple
deps =
   pytest
   mock

[testenv:py{3}]
commands = pytest -rP --junitxml=xunit_results/junit-{envname}.xml

[testenv:coverage]
deps =
    {[testenv]deps}
    coverage
commands =
    coverage run --source qnx_src_package -m pytest -rP --junitxml=xunit_results/junit-{envname}.xml
	coverage report -m
	coverage html

[flake8]
max-line-length = 120
