============
Contributing
============

Contributions are welcome, and they are greatly appreciated! Every
little bit helps, and credit will always be given.

You can contribute in many ways:

Types of Contributions
----------------------

Report Bugs
~~~~~~~~~~~

Report bugs at https://jira.bbqnx.net/browse/INFRA

Write Documentation
~~~~~~~~~~~~~~~~~~~

qnx_src_package could always use more documentation, whether as part of the
official qnx_src_package docs, in docstrings, or even on the wiki.

Wiki: https://wikis.rim.net/display/QNXInfra/QNX+Python+Packages

Submit Feedback
~~~~~~~~~~~~~~~

The best way to send feedback is <NAME_EMAIL>


Get Started!
------------

Ready to contribute? Here's how to set up `qnx_src_package` for local development.

1. Checkout the `qnx_src_package` repo from gitlab - https://gitlab.rim.net/qnx/infra/pypi/qnx_src_package.
2. Use pip install -e (via make dev) to setup locally::

    $ cd qnx_src_package
    $ make dev

3. Now you can make your changes locally.

4. When you're done making changes, bump the version, check that your changes pass flake8 and the tests, including testing other Python versions with tox::

    $ bumpversion patch
    $ make lint 
    $ make test
    $ make test-all

5. Post a Merge Request to gitlab following BlackBerry QNX process

Commit Guidelines
-----------------------

Before you submit a merge request, check that it meets these guidelines:

1. The change should include tests.
2. If the review request adds functionality, the docs should be updated. Put
   your new functionality into a function with a docstring, and add the
   feature to the list in README.rst.
3. The request should work for the versions of Python specified in setup.py and for PyPy. Run tox
   and make sure that the tests pass for all supported Python versions.

Tips
----

To run a subset of tests::

    $ python -m unittest tests.test_qnx_src_package
