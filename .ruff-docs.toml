# Documentation related rules extended config:
# Meant to be ran as a parralel linting pipeline (non-blocking in most cases)

# Base config
extend = ".ruff.toml"

# Small set of documentation related rules
select = [
    "D", # pydocstyle (docstring linting)
    "ANN", # annotations (mypy could make this obsolete? vice versa?)
]

# Ignore certain opinionated rules
ignore = [
    "D100", # undocumented-public-module
    "D212", # multi-line-summary-first-line
]