.PHONY: clean-pyc clean-build docs clean

PYTHON := python3

define BROWSER_PYSCRIPT
import os, webbrowser, sys
try:
	from urllib import pathname2url
except:
	from urllib.request import pathname2url

webbrowser.open("file://" + pathname2url(os.path.abspath(sys.argv[1])))
endef
export BROWSER_PYSCRIPT

BROWSER := ${PYTHON} -c "$$BROWSER_PYSCRIPT"

help:
	@echo "clean - remove all build, test, coverage and Python artifacts"
	@echo "clean-build - remove build artifacts"
	@echo "clean-pyc - remove Python file artifacts"
	@echo "clean-test - remove test and coverage artifacts"
	@echo "lint - check style with flake8"
	@echo "test - run tests quickly with the default Python"
	@echo "test-all - run tests on every Python version with tox"
	@echo "coverage - check code coverage quickly with the default Python"
	@echo "docs - generate Sphinx HTML documentation, including API docs"
	@echo "release - package and upload a release"
	@echo "dist - package"
	@echo "install - install the package to the active Python's site-packages"
	@echo "dev - install the package in development mode, with all development dependencies"

clean: clean-build clean-pyc clean-test

clean-build:
	rm -fr build/
	rm -fr dist/
	rm -fr .eggs/
	find . -name '*.egg-info' -exec rm -fr {} + 2>/dev/null || true
	find . -name '*.egg' -exec rm -f {} + 2>/dev/null || true

clean-pyc:
	find . -name '*.pyc' -exec rm -f {} + 2>/dev/null || true
	find . -name '*.pyo' -exec rm -f {} + 2>/dev/null || true
	find . -name '*~' -exec rm -f {} + 2>/dev/null || true
	find . -name '__pycache__' -exec rm -fr {} + 2>/dev/null || true

clean-test:
	rm -fr .tox/
	rm -f .coverage
	rm -fr htmlcov/
	rm -fr xunit_results && mkdir xunit_results
	
lint:
	${PYTHON} -m ruff check src/qnx_src_package tests --config .ruff.toml
	${PYTHON} -m black src/qnx_src_package tests --line-length 120

lint-fix:
	${PYTHON} -m ruff check src/qnx_src_package tests --config .ruff.toml --fix

lint-docs:
	${PYTHON} -m ruff check src/qnx_src_package tests --config .ruff-docs.toml
	${PYTHON} -m black src/qnx_src_package tests --line-length 120

lint-strict:
	${PYTHON} -m ruff check src/qnx_src_package tests --config .ruff-strict.toml
	${PYTHON} -m black src/qnx_src_package tests --line-length 120

test:
	tox -e py3

test-all:
	tox

coverage:
	tox -e coverage
	$(BROWSER) htmlcov/index.html

docs:
	rm -f docs/qnx_src_package.rst
	rm -f docs/modules.rst
	sphinx-apidoc -o docs/ src/qnx_src_package
	$(MAKE) -C docs clean
	$(MAKE) -C docs html
	$(BROWSER) docs/_build/html/index.html

servedocs: docs
	watchmedo shell-command -p '*.rst' -c '$(MAKE) -C docs html' -R -D .

release: clean
	${PYTHON} -m build
	${PYTHON} -m twine upload --repository local -u ${PYPI_USER} -p ${PYPI_PASS} dist/*

dist: clean
	${PYTHON} -m build --sdist
	${PYTHON} -m build --wheel
	ls -l dist

install: clean
	${PYTHON} -m pip install -e .

dev: clean
# install the project in development mode, which ensures that changes
# to the local copy are mirrored in the installation
	${PYTHON} -m pip install -e .
# use pip to install the dependencies specified in the "dev"
# optional requirements list under extras_require in setup.py
	${PYTHON} -m pip install -e .[dev]
